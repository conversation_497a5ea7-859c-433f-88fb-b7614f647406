"use client";
import { useState } from "react";
import Image from "next/image";
import { FaPlus, FaMinus } from "react-icons/fa6";

const values = [
  {
    title: "Professionalism",
    description:
      "We always commit to do  our projects in a  workmanship like manner.",
  },
  {
    title: "Team Work",
    description:
      "We know we can only  succeed together or fail  together as a team",
  },
  {
    title: "Leadership",
    description:
      "Everyone in our team is a  leader. We believe in  leading from the bottom up.",
  },
  {
    title: "Integrity",
    description:
      "We believe in doing things  right & within the confines  of the laws of the land",
  },
  {
    title: "Honesty",
    description:
      "We always strive to be  honest to our clients in  project delivery.",
  },
];

export default function OurPrincipals() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleItem = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="bg-background py-16 md:py-24">
      <div className="container mx-auto max-w-6xl px-6 md:px-8">
        <div className="grid grid-cols-1 items-start gap-12 lg:grid-cols-2 lg:gap-16">
          {/* Left Section */}
          <div className="order-2 lg:order-1">
            <h2 className="text-pretty text-4xl leading-tight md:text-6xl font-medium text-foreground mb-10 max-w-2xl">
              Our principals
            </h2>

            <div className="divide-y divide-border border-t border-border">
              {values.map((v, i) => (
                <div key={i} className="border-b border-border">
                  {/* Header Row */}
                  <button
                    onClick={() => toggleItem(i)}
                    className="flex w-full items-center justify-between py-6 text-left"
                  >
                    <h3 className="text-lg md:text-2xl font-medium text-foreground">
                      {v.title}
                    </h3>
                    <div className="flex size-8 items-center justify-center rounded-full border border-border text-foreground shrink-0">
                      {openIndex === i ? (
                        <FaMinus className="text-base" />
                      ) : (
                        <FaPlus className="text-base" />
                      )}
                    </div>
                  </button>

                  {/* Expandable Content */}
                  <div
                    className={`overflow-hidden transition-all duration-300 ${
                      openIndex === i
                        ? "max-h-40 opacity-100"
                        : "max-h-0 opacity-0"
                    }`}
                  >
                    <p className="text-sm leading-relaxed text-muted-foreground md:text-base pb-6 pr-10">
                      {v.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Section (Image) */}
          <figure className="order-1 lg:order-2">
            <div className="relative aspect-[6/5] w-full overflow-hidden rounded-3xl border border-border bg-secondary">
              <Image
                src="/background3.jpg"
                alt="Award-style graphic on a blue gradient background representing culture and values."
                fill
                className="object-cover"
                priority
              />
            </div>
          </figure>
        </div>
      </div>
    </section>
  );
}
