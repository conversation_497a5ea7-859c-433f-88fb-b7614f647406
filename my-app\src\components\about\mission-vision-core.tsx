"use client";

import React from "react";
import { motion } from "framer-motion";
import { Target, Eye, Heart } from "lucide-react";

const MissionVisionCore: React.FC = () => {
  const coreValues = [
    "High quality services",
    "Integrity with customers and partners",
    "Building relationships on trust",
  ];

  return (
    <section
      className="relative overflow-hidden py-16 sm:py-24 bg-fixed bg-cover bg-center"
      style={{ backgroundImage: "url(/about-hero.jpg)" }}
    >

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 text-white">
        <div className="text-center mb-16">
          <h2 className="text-base font-semibold leading-7 text-blue-300">
            Our Guiding Principles
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
            Our Vision Mission & Core Values
          </p>
          <p className="mt-6 text-lg leading-8 max-w-2xl mx-auto text-gray-300">
            We provide expert HVAC services all year round. Whether you need
            ventilation services, cooling services or even hot water services,
            ozonecool is your ideal partner. With expertise in both residential
            and commercial properties.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Mission Card (No changes) */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white bg-opacity-10 p-8 rounded-xl shadow-lg border border-white border-opacity-20 hover:bg-opacity-20 transition duration-300 transform hover:-translate-y-1"
          >
            <div className="flex flex-col items-center text-center">
              <Target className="h-10 w-10 text-blue-400 mb-3" />
              <h3 className="text-2xl font-semibold mb-3">Our Mission</h3>
              <p className="text-gray-200 leading-relaxed text-sm">
                Our vision is to be the leader in providing customer centric and
                professional HVAC and Green Energy products to our customers.
              </p>
            </div>
          </motion.div>

          {/* Vision Card (No changes) */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white bg-opacity-10 p-8 rounded-xl shadow-lg border border-white border-opacity-20 hover:bg-opacity-20 transition duration-300 transform hover:-translate-y-1"
          >
            <div className="flex flex-col items-center text-center">
              <Eye className="h-10 w-10 text-blue-400 mb-3" />
              <h3 className="text-2xl font-semibold mb-3">Our Vision</h3>
              <p className="text-gray-200 leading-relaxed text-sm">
                Our mission is to provide professional engineering services in
                designing, installing, testing, commissioning & servicing of
                HVAC and Green Energy systems.
              </p>
            </div>
          </motion.div>

          {/* --- Core Values Card (Updated) --- */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-white bg-opacity-10 p-8 rounded-xl shadow-lg border border-white border-opacity-20 hover:bg-opacity-20 transition duration-300 transform hover:-translate-y-1 lg:col-span-1 md:col-span-2 md:col-start-1 lg:col-start-auto"
          >
            {/* --- CHANGE 2: Made the entire card content centered --- */}
            <div className="flex flex-col items-center text-center">
              <Heart className="h-10 w-10 text-blue-400 mb-3" />
              <h3 className="text-2xl font-semibold mb-3"> Our Core Values</h3>
              <p className="text-gray-200 leading-relaxed text-sm mb-4">
                We believe in:
              </p>
              {/* --- CHANGE 3: Removed bullet points for a cleaner look --- */}
              <div className="space-y-2 text-gray-200 text-sm">
                {coreValues.map((value, index) => (
                  <p key={index}>{value}</p>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default MissionVisionCore;
