"use client";

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

type TimelineItem = {
  year: string;
  title: string;
  description: string;
};

const defaultItems: TimelineItem[] = [
  {
    year: "2016",
    title: "Establishment",
    description:
      "We started off as business  specializing in the provision of  HVAC services",
  },
  {
    year: "2017",
    title: "Incorporation",
    description:
      "We become a fully fledged  registered company incorporated in India",
  },
  {
    year: "2018",
    title: "Team Growth",
    description:
      "Our team started growing according to market demand.",
  },
  {
    year: "2019",
    title: "Product Diversification",
    description:
      "We diversify our product portfolio  and venture into core HVAC Equipments and Design",
  },
  {
    year: "2020",
    title: "Milestone",
    description:
      "To become trusted partner of various business house in operation and maintenance of HVAC equipments",
  },
];

export function Journey({
  items = defaultItems,
  heading = "Our journey",
  subheading = "Each achievement reflects our commitment to excellence and growth.",
}: {
  items?: TimelineItem[];
  heading?: string;
  subheading?: string;
}) {
  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    const section = sectionRef.current;
    if (!section) return;

    let rafId: number | null = null;

    const computeProgressAndIndex = () => {
      const rect = section.getBoundingClientRect();
      const vh = window.innerHeight;

      // Prevent early/late drift by clamping scroll effect strictly within viewport range
      // Start immediately when section enters the viewport
      const start = 0;
      const end = rect.height - vh;
      const scrollY = -rect.top; // how far we've scrolled into the section
      const clamped = Math.min(Math.max(scrollY - start, 0), end - start);
      const progress = end > start ? clamped / (end - start) : 0;

      const newIndex = Math.round(progress * (items.length - 1));
      setActiveIndex((prev) => (prev === newIndex ? prev : newIndex));
    };

    const onScroll = () => {
      if (rafId !== null) return;
      rafId = requestAnimationFrame(() => {
        computeProgressAndIndex();
        rafId = null;
      });
    };

    computeProgressAndIndex();

    window.addEventListener("scroll", onScroll, { passive: true });
    window.addEventListener("resize", onScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", onScroll);
      window.removeEventListener("resize", onScroll);
      if (rafId) cancelAnimationFrame(rafId);
    };
  }, [items.length]);

  const gutterPct = 4;
  const progressWidthPct =
    items.length > 1
      ? (activeIndex / (items.length - 1)) * (100 - 2 * gutterPct)
      : 0;

  return (
    <section
      ref={sectionRef}
      className="relative bg-[#272fcc]"
      style={{ height: "250vh" }}
    >
      {/* Sticky viewport panel */}
      <div className="sticky top-0 h-screen flex items-center">
        <div className="w-full py-4 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 24 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-10"
          >
            <div className="flex items-center gap-2 mb-4">
              <span className="sr-only">Section</span>
              <div className="w-2 h-2 rounded-full bg-white/70" />
              <span className="text-xs tracking-wider uppercase text-white/60">
                Story
              </span>
            </div>
            <h2 className="text-3xl md:text-6xl font-medium text-white text-balance mb-3">
              {heading}
            </h2>
            <p className="text-base md:text-lg text-white/60 leading-relaxed max-w-2xl">
              {subheading}
            </p>
          </motion.div>

          {/* Timeline */}
          <div className="relative w-full mt-16 mb-10 h-24">
            {/* Year labels */}
            {items.map((item, idx) => {
              const leftPct =
                items.length > 1
                  ? gutterPct +
                    (idx / (items.length - 1)) * (100 - 2 * gutterPct)
                  : gutterPct;
              const isActive = idx === activeIndex; // 🔥 FIXED: only one active at a time
              return (
                <div
                  key={`label-${idx}`}
                  className="absolute bottom-8 md:bottom-10"
                  style={{
                    left: `${leftPct}%`,
                    transform: "translate(-50%, 0)",
                  }}
                >
                  <motion.span
                    initial={false}
                    animate={{ opacity: isActive ? 1 : 0.55 }}
                    transition={{ duration: 0.25 }}
                    className={cn(
                      "font-medium whitespace-nowrap text-xl sm:text-3xl md:text-4xl lg:text-6xl",
                      isActive ? "text-white" : "text-white/60"
                    )}
                  >
                    {item.year}
                  </motion.span>
                </div>
              );
            })}

            {/* Base line */}
            <div
              aria-hidden="true"
              className="absolute bottom-0 h-px bg-white/30"
              style={{ left: `${gutterPct}%`, right: `${gutterPct}%` }}
            />

            {/* Progress line */}
            <motion.div
              aria-hidden="true"
              className="absolute bottom-0 h-px bg-white/70"
              style={{ left: `${gutterPct}%`, width: `${progressWidthPct}%` }}
              animate={{ width: `${progressWidthPct}%` }}
              transition={{ type: "tween", duration: 0.35 }}
            />

            {/* Markers */}
            {items.map((item, idx) => {
              const leftPct =
                items.length > 1
                  ? gutterPct +
                    (idx / (items.length - 1)) * (100 - 2 * gutterPct)
                  : gutterPct;
              const isActive = idx === activeIndex; //FIXED: only current marker highlighted
              return (
                <div
                  key={`dot-${idx}`}
                  className="absolute bottom-0"
                  style={{
                    left: `${leftPct}%`,
                    transform: "translate(-50%, 50%)",
                  }}
                >
                  <motion.div
                    className={cn(
                      "rounded-full",
                      isActive ? "bg-white" : "bg-white/45"
                    )}
                    style={{ width: 6, height: 6 }}
                    initial={false}
                    animate={{ scale: isActive ? 1.2 : 1 }}
                    transition={{ duration: 0.2 }}
                  />
                  <span className="sr-only">{`Milestone ${item.year}`}</span>
                </div>
              );
            })}
          </div>

          {/* Active Description */}
          <motion.div
            key={activeIndex}
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.35 }}
            className="max-w-2xl"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-4 text-white">
              {items[activeIndex]?.title}
            </h3>
            <div className="text-sm md:text-lg text-white/70 leading-relaxed">
              {items[activeIndex]?.description}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

export default Journey;
