"use client";
import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";

// 1. New data structure matching the image's content style
const photoGallery = [
  {
    id: 1,
    src: "/background3.jpg",
    price: 16,
    author: "Александр Иванов",
    className: "row-span-1 md:row-span-2",
  },
  {
    id: 2,
    src: "/background2.jpg",
   
    className: "col-span-1 md:col-span-2",
  },
  {
    id: 3,
    src: "/background1.jpg",
    
    className: "", // Standard size
  },
  {
    id: 4,
    src: "/background.jpg",
   
    className: "row-span-1 md:row-span-2",
  },
  {
    id: 5,
    src: "/background2.jpg",
    
    className: "", // Standard size
  },
  {
    id: 6,
    src: "/background3.jpg",
    className: "", // Standard size
  },
  {
    id: 7,
    src: "/background.jpg",
    
    className: "", // Standard size
  },
  {
    id: 8,
    src: "/background2.jpg",
    
    className: "", // Standard size
  },
  {
    id: 9,
    src: "/background3.jpg",
    
    className: "", // Standard size
  },
];

// Animation variants for the container to orchestrate children animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Animation variants for each photo item
const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.5 } },
};

export default function RedesignedGallery() {
  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <p className="mt-2 text-4xl font-bold text-gray-900 tracking-tight sm:text-5xl">
           Our Gallery
          </p>
        </div>

        {/* 2. CSS Grid container for the masonry layout */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-4 auto-rows-[200px] md:auto-rows-[250px]"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {photoGallery.map((photo) => (
            <motion.div
              key={photo.id}
              className={`relative overflow-hidden rounded-xl group cursor-pointer ${photo.className}`}
              variants={itemVariants}
            >
              <Image
                src={photo.src}
                alt={`Photo by ${photo.author}`}
                fill
                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 25vw"
                className="object-cover w-full h-full transition-transform duration-500 ease-in-out group-hover:scale-110"
              />
              {/* 3. Gradient overlay positioned at the bottom */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
