"use client";

import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'

const AboutSection = () => {
  
  

  return (
    <section className="relative overflow-hidden py-16 lg:py-20">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/50 to-indigo-50/30" />
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 items-start">
          {/* Left Content - Text */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-7 space-y-8"
          >
            {/* Content Cards */}
            <div className="space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div>
                    <h3 className="text-4xl font-bold text-gray-900 mb-6">
                      Read About Our Company
                    </h3>
                    <p className="text-base text-[#7d7d7d] font-medium leading-relaxed mb-4">
                      We would like to take the opportunity to introduce
                      ourselves for installation & maintenance of your HVAC & LT
                      Electrical System. S T HVAC SERVICES is committed to serve
                      you best, and will be stubborn on the point 24X7.
                    </p>
                    <p className="text-base text-[#7d7d7d] font-medium leading-relaxed mb-4">
                      Our Super skilled, Semi Skilled and Skilled Technicians
                      will be equipped with all tools and tackles to meet the
                      demand on time.
                    </p>
                    <p className="text-base text-[#7d7d7d] font-medium leading-relaxed mb-4">
                      We are committed to you in the strategic design and
                      seamless execution of innovative, state-of-the-art
                      technology HVAC systems that are delivered within budget
                      and agreed time scales. We also operates an after sales
                      support, providing functional and technological repair and
                      maintenance services for their products.
                    </p>
                    <p className="text-base text-[#7d7d7d] font-medium leading-relaxed">
                      Starting from Supply, Installation, Commissioning &
                      Testing (SITC), field and warranty support to on going
                      help desk, carry-in repair and reverse logistics, S T HVAC
                      delivers 'end to end' service solutions, designed to
                      fulfil and enhance service level commitments, enabling
                      partners to better serve their customers in an efficient
                      and cost effective manner.
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Content - Image and Highlights */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="lg:col-span-5 relative"
          >
            {/* Main Image Container */}
            <div className="relative">
              {/* Main Image */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="relative h-[500px] lg:h-[540px] rounded-3xl overflow-hidden shadow-2xl group"
              >
                <Image
                  src="/about1.jpg"
                  alt="S T HVAC Services - Professional HVAC Installation"
                  fill
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                  sizes="(max-width: 768px) 100vw, 40vw"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

                {/* Overlay Content */}
                <div className="absolute bottom-6 left-6 right-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                    viewport={{ once: true }}
                    className="bg-white/90 backdrop-blur-md rounded-2xl p-6 shadow-xl"
                  >
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      Professional Excellence
                    </h3>
                    <p className="text-gray-700 text-sm">
                      Delivering state-of-the-art HVAC solutions with precision
                      and reliability.
                    </p>
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

export default AboutSection
