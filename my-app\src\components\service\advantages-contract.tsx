"use client";

import React from 'react';
import { motion } from 'framer-motion';

const AdvantagesContract = () => {
  const advantages = [
    {
      id: 1,
      icon: "💰",
      title: "Cost Savings",
      description: "AMC services save from unexpected maintenance and repair costs. Systematically & timely executed services with round-the-clock maintenance from qualified technicians.",
      highlight: "Predictable Budgeting"
    },
    {
      id: 2,
      icon: "📅",
      title: "Planned Maintenance Services",
      description: "Effective planning of maintenance and repair services at regular intervals for air conditioners and heavy machinery to ensure optimal performance.",
      highlight: "Scheduled Care"
    },
    {
      id: 3,
      icon: "🔧",
      title: "Genuine Spare Parts",
      description: "Access to only genuine spare parts for machinery and air conditioners, ensuring better performance, reliability, and extended equipment life.",
      highlight: "Quality Assured"
    },
    {
      id: 4,
      icon: "🛠️",
      title: "24/7 Technical Support",
      description: "Round-the-clock maintenance & repair services from qualified technicians and support staff ensures your equipment is always running optimally.",
      highlight: "Always Available"
    },
    {
      id: 5,
      icon: "⏱️",
      title: "Extended Equipment Life",
      description: "Regular maintenance and use of genuine parts significantly extends the lifespan of your HVAC equipment and machinery, maximizing your investment.",
      highlight: "Long-term Value"
    },
    {
      id: 6,
      icon: "📊",
      title: "Performance Monitoring",
      description: "Continuous monitoring and performance analysis to identify potential issues before they become costly problems, ensuring peak efficiency.",
      highlight: "Proactive Care"
    }
  ];

  const benefits = [
    { icon: "✅", text: "Guaranteed SLA compliance" },
    { icon: "🎯", text: "Priority service response" },
    { icon: "📈", text: "Improved equipment efficiency" },
    { icon: "🔒", text: "Warranty protection" }
  ];

  return (
    <section className="relative py-20 bg-gradient-to-br from-gray-50 via-blue-50/30 to-gray-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-32 left-20 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div className="absolute bottom-32 right-20 w-80 h-80 bg-cyan-300 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative container mx-auto px-4 lg:px-8 max-w-7xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-4 py-2 text-sm font-semibold tracking-wider text-blue-800 ring-1 ring-inset ring-blue-200 mb-6">
            🏆 AMC Benefits
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Advantages of Annual
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent block mt-2">
              Maintenance Contract
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-cyan-600 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover the comprehensive benefits of our annual maintenance contracts
            that ensure optimal performance and longevity of your HVAC systems.
          </p>
        </motion.div>

        {/* Advantages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {advantages.map((advantage, index) => (
            <motion.div
              key={advantage.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group relative bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
            >
              <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <span className="text-xs font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
                  {advantage.highlight}
                </span>
              </div>

              <div className="flex items-start gap-6 mb-4">
                <div className="text-4xl group-hover:scale-110 transition-transform duration-300">
                  {advantage.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {advantage.title}
                  </h3>
                </div>
              </div>

              <p className="text-gray-600 leading-relaxed">
                {advantage.description}
              </p>

              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AdvantagesContract;