"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

const Philosophy = () => {
  const coreValues = [
    {
      title: "Our Philosophy",
      description: "Being always there for our customers. Excellent support and service is the backbone  of ST HVAC.It's one of our distinguishing featuresand strength.Just as you can rely on STHVAC SERVICES for our valued commitments and solutions which never fail.For thorough reliability and responsiveness, ST HVAC SERVICES is managed though a highly efficient Personnel.ContinuousVisit of Engineers and Disciplinary Staffs will be done as committed.As one of India's largest chains in organized repair, we also extends direct to customer's service and supportTo offer a great working environment for employees, clients, vendors and stakeholders by sustaining long-term  growth and customer satisfaction. We offer high-quality services at unbeatable prices and give you a pleasant  and satisfying personalized experience that suits your business requirements. We possess a deep  understanding of the needs of modern businesses. We understand what you need to stand out from the crowd.  Using innovation and technology, we will make sure that you leave your competition miles behind.We operates across India. More than 150+ support vendor and logistics to reach every corner in India. We are  one of the strongest player in the market to serve quality support with guaranteed SLA because we only focus on  what we are good in. We are happy to announce that now we are the service partner of more than 12+ brands  and still signing."
    },
  ];
  return (
    <section className="relative overflow-hidden py-24 lg:py-32">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/50 to-indigo-50/30" />
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 items-start">
          {/* Left Content - Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-5 relative"
          >
            {/* Main Image Container */}
            <div className="relative">
              {/* Main Image */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="relative h-[500px] lg:h-[540px] rounded-3xl overflow-hidden shadow-2xl group"
              >
                <Image
                  src="/about1.jpg"
                  alt="S T HVAC Services - Our Philosophy"
                  fill
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                  sizes="(max-width: 768px) 100vw, 40vw"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

                {/* Overlay Content */}
                <div className="absolute bottom-6 left-6 right-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                    viewport={{ once: true }}
                    className="bg-white/90 backdrop-blur-md rounded-2xl p-6 shadow-xl"
                  >
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      Our Commitment
                    </h3>
                    <p className="text-gray-700 text-sm">
                      Dedicated to excellence in HVAC services with unwavering customer focus.
                    </p>
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Content - Text */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="lg:col-span-7 space-y-8"
          >
            {/* Content Cards */}
            <div className="space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div>
                    <h3 className="text-4xl font-bold text-gray-900 mb-6">
                      {coreValues[0].title}
                    </h3>
                    <p className="text-base text-[#7d7d7d] font-medium leading-relaxed">
                      {coreValues[0].description}
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Philosophy;
